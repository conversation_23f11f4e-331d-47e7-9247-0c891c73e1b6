import { useEffect, useState } from 'react';

export function useQueryFix({ query, transform }) {
  const { isLoading, error, data, refetch, isFetching } = query;

  const [localData, setLocalData] = useState(() => {
    try {
      return data ? transform(data) : undefined;
    } catch (error) {
      console.error('Transform error in useQueryFix:', error);
      return undefined;
    }
  });
  const [localLoading, setLocalLoading] = useState(isLoading);

  useEffect(() => {
    if (isLoading) {
      setLocalLoading(true);
    }

    if (error && localLoading) {
      setLocalLoading(false);
    }

    if (data) {
      try {
        setLocalData(transform(data));
        setLocalLoading(false);
      } catch (error) {
        console.error('Transform error in useQueryFix useEffect:', error);
        setLocalData(undefined);
        setLocalLoading(false);
      }
    }
  }, [isLoading, error, data, transform, localLoading]);

  const handleRefetch = async () => {
    setLocalLoading(true);
    await refetch();
    setLocalLoading(false);
  };

  return {
    data: localData,
    loading: localLoading,
    error,
    refetch: handleRefetch,
    isFetching
  };
}
