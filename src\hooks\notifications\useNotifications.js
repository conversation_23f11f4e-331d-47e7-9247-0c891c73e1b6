import {useQuery} from "react-query";
import {getAccessToken, useQueryFix} from "../../vbrae-utils/index.js";
import {getNotifications} from "../../api/notifications/getNotifications.js";

export const useNotifications = (props) => {
    const { data:notifications, loading: notificationLoading} = useQueryFix({
        query: useQuery({
            queryKey: ['notifications'],
            queryFn: () => getNotifications(props),
            refetchOnWindowFocus: false,
            enabled: !!getAccessToken()
        }),
        transform: (response) => response?.data || [],
    });

    return { notifications: notifications || [], notificationLoading};
};