import {Link, useParams} from "react-router-dom";
import AccountSideBar from "../componets/account/AccountSideBar";
import MainHeader from "../componets/MainHeader";
import MainFooter from "../componets/MainFooter";
import {useTicketDetails} from "../../hooks/ticket/useTicketDetails.js";
import Spinner from "../../components/common/Spinner.jsx";
import {convertToDays} from "../../vbrae-utils/lib/time.js";
import {useTicketUpdate} from "../../hooks/ticket/useTicketUpdate.js";
import {useEffect, useState} from "react";
 import SupportMessenger from "../../components/ticket/SupportMessenger.jsx";

export default function SupportDetails() {

  const breadCrums = [
    {
      title: "Customer Support",
      url: "/account/support",
    },
  ];

  const breadCrumsList = breadCrums?.map((item, index) => {
    return (
      <Link to={item.url} key={index}>
        <p className="crumb_link">/ {item.title}</p>
      </Link>
    );
  });

  return (
    <>
      <div
        className="d-flex w-100 main_house_con"
        style={{
          background: "none",
        }}>
        <AccountSideBar activeLink={"support"} />

        <div className="col housing_con d-flex flex-column">
          <MainHeader
            showHeader={true}
            isAccount={true}
            breadCrums={breadCrums}
            activeLink={"support"}
          />

          <div id="scrollable-section" className="col acct_cont_con pt-lg-0">
            <div className=" d-lg-none justify-content-between align-items-center mb-3">
              <div className="d-flex align-items-center gap-2">
                <Link to={"/"}>
                  <span className="crumb_icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="1em"
                      height="1em"
                      viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M13.45 2.533a2.25 2.25 0 0 0-2.9 0L3.8 8.228a2.25 2.25 0 0 0-.8 1.72v9.305c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V15.25c0-.68.542-1.232 1.217-1.25h2.566a1.25 1.25 0 0 1 1.217 1.25v4.003c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V9.947a2.25 2.25 0 0 0-.8-1.72z"
                      />
                    </svg>
                  </span>
                </Link>
                {breadCrumsList}
              </div>
            </div>

            <RenderTicket />

            <div className="col d-lg-none mt-4">
              <MainFooter />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

function RenderTicket() {

  const {id = ''} = useParams();
  const [state, setState] = useState({status: ''});
  const {ticketUpdate, updateLoading} = useTicketUpdate({_id:id, state})
  const {ticketDetails, ticketDetailsLoading} = useTicketDetails({_id: id});

  useEffect(() => {
    if(!state.status) return;

    ticketUpdate().finally(()=> setState({status: ''}));
  }, [state, ticketUpdate]);

  if(ticketDetailsLoading) return <Spinner />
  console.log(ticketDetails?.conversationId);
  if(!ticketDetails) {
    return (
      <div className="acct_cont">
        <div className="text-center p-4">
          <h3>Ticket not found</h3>
          <p>The ticket you&apos;re looking for doesn&apos;t exist or you don&apos;t have permission to view it.</p>
        </div>
      </div>
    )
  }

  return (
      <div className="acct_cont">
        <div className="col d-flex justify-content-between align-items-center">
          <p className="acct_offer_title ">{ticketDetails?.ticketNumber || 'N/A'}</p>
          <button type="button" className="acct_offer_btn3" disabled={updateLoading || ticketDetails?.status === "closed"} onClick={()=> setState({status: "closed"})}>
            {updateLoading ? "Closing..." : "Close Ticket"}
          </button>
        </div>

        <hr className="acct_fil_line my-3"/>

        <div className="col d-flex flex-wrap gap-3 mb-5">
          <div className="col acct_offer_cont">
            <div className="col d-flex flex-wrap mb-4">
              <div className="col-12 mb-3">
                <p className="acct_head_sm mb-1">Subject</p>
                <p className="acct_head">{ticketDetails?.subject || 'N/A'}</p>
              </div>
              <div className="col-6 col-md pe-3 mb-3">
                <p className="acct_head_sm mb-1">ID </p>
                <p className="acct_head_s">#{ticketDetails?.ticketNumber?.split("-")?.[1] || 'N/A'}</p>
              </div>
              <div className="col-6 col-md mb-3">
                <p className="acct_head_sm mb-1">Status</p>
              

   <span
                className={`
    acct_table_data_tag 
    ${
      ticketDetails?.status === "closed"
        ? "green"
        : ticketDetails?.conversationId?.messages?.length > 0
        ? "blue" // add this class in your CSS for "responding"
        : "yellow"
    }
  `}
              >
                {ticketDetails?.status === "closed"
                  ? "CLOSE"
                  : ticketDetails?.conversationId?.messages?.length > 0
                  ? "RESPONDING"
                  : "OPEN"}
              </span>


              </div>
              <div className="col-6 col-md pe-3">
                <p className="acct_head_sm mb-1">Date </p>
                <p className="acct_head_s">{ticketDetails?.createdAt ? convertToDays(ticketDetails.createdAt) : 'N/A'}</p>
              </div>
              <div className="col-6 col-md">
                <p className="acct_head_sm mb-1">Updated</p>
                <p className="acct_head_s">{ticketDetails?.updatedAt ? convertToDays(ticketDetails.updatedAt) : 'N/A'}</p>
              </div>
            </div>
          </div>
        </div>

         <div className="col acct_offer_cont">
          <p className="acct_head_smm mb-3">Recent Activity</p>

           <SupportMessenger conversationData={ticketDetails?.conversationId} />
        </div>
      </div>
  )
}