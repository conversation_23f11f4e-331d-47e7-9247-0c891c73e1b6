import {convertToDays} from "../../vbrae-utils/lib/time.js";
import CustomDropdown from "../../pages/componets/utility/CustomDropdown.jsx";
import Spinner from "../common/Spinner.jsx";
import {Link} from "react-router-dom";
import {useEffect, useState} from "react";
import useDeleteNotification from "../../hooks/notifications/useDeleteNotification.js";
import {useMarkNotificationRead} from "../../hooks/notifications/useMarkNotificationRead.js";

function selectRoute(data){
    switch(data.entityType){
        case 'conversation-offer':
            return `/account/messages?conversationId=${data.entity._id}`
        case 'wishlist':
            return '/account/wishlist'
        case 'review':
            return '/account/messages'
        case 'order':
            return `/account/order-details/${data.entityId}`
        case 'conversation-ticket':
            return `/account/support-details/${data.entityId}?conversationId=${data.entity._id}`
    }
}

export default function NotificationItem(item){

    const [notificationToDelete, setNotificationToDelete] = useState("");
    const {deleteRefetch}= useDeleteNotification({_id: item._id});
    const {markNotificationAsRead} = useMarkNotificationRead();

    useEffect(() => {
        if(!notificationToDelete) return;
        deleteRefetch().finally(()=> setNotificationToDelete(""))
    }, [notificationToDelete, deleteRefetch]);

    const handleNotificationClick = () => {
        // Mark notification as read when clicked
        if (!item.isRead) {
            markNotificationAsRead(item._id);
        }
    };

    return (
        <Link
            className={`acct_noti_cont lin_bg d-flex align-items-center gap-3 mb-2 ${!item.isRead ? 'unread-notification' : ''}`}
            to={selectRoute(item)}
            onClick={handleNotificationClick}
        >
            <div className="position-relative">
                <img
                    src={item.entity?.templateCoverImage || "https://walrus-app-yy5iv.ondigitalocean.app/assets/images/logo.png"}
                    alt=""
                    className="acct_noti_img"
                />
                {!item.isRead && (
                    <span className="position-absolute top-0 start-100 translate-middle p-1 bg-primary border border-light rounded-circle">
                        <span className="visually-hidden">Unread notification</span>
                    </span>
                )}
            </div>
            <div className="col">
                <p className="acct_noti_date mb-1">{convertToDays(item.createdAt)}</p>
                <p className={`acct_noti_title ${!item.isRead ? 'fw-bold' : ''}`}>
                    {item.title || item.message}
                </p>
            </div>
            <div
                onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                }}
            >
                <CustomDropdown
                    trigger={() => (
                        <span className="mob_table_data_icon">
                            {notificationToDelete ? (
                                <Spinner />
                            ) : (
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="1em"
                                    height="1em"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        fill="currentColor"
                                        d="M12 3c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2m0 14c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2m0-7c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2"
                                    />
                                </svg>
                            )}
                          </span>
                    )}
                    content={
                        <div className="acct_table_drop_cont">
                          <span
                              onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  setNotificationToDelete({ _id: item._id });
                              }}
                          >
                            <p className="acct_table_drop_link">Delete</p>
                          </span>
                        </div>
                    }
                />
            </div>
        </Link>
    )
}