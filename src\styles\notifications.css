/* Notification Styles */

.unread-notification {
    background-color: #f8f9fa !important;
    border-left: 3px solid #007bff;
}

.unread-notification:hover {
    background-color: #e9ecef !important;
}

.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background-color: #dc3545;
    border-radius: 50%;
    border: 2px solid white;
}

.notification-item {
    transition: background-color 0.2s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

/* Header notification dropdown styles */
.header_drop_cont .bg-light {
    background-color: #f8f9fa !important;
}

.header_drop_cont .bg-light:hover {
    background-color: #e9ecef !important;
}

/* Notification count badges */
.notification-count {
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 0.75rem;
    font-weight: bold;
    min-width: 18px;
    text-align: center;
}

/* Unread indicator dot */
.unread-dot {
    width: 8px;
    height: 8px;
    background-color: #007bff;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

/* Tab styles for notification page */
.dash_tab_link.active {
    color: #007bff !important;
    border-bottom: 2px solid #007bff;
}

.dash_tab_link {
    cursor: pointer;
    padding: 8px 12px;
    transition: color 0.2s ease;
}

.dash_tab_link:hover {
    color: #0056b3;
}
