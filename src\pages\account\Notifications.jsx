import React from "react";
import {Link} from "react-router-dom";
import AccountSideBar from "../componets/account/AccountSideBar";
import MainHeader from "../componets/MainHeader";
import {useTabs} from "../../services/CustomTabs";
import MainFooter from "../componets/MainFooter";
import {useNotifications} from "../../hooks/notifications/useNotifications";
import Spinner from "../../components/common/Spinner";
import {useNotificationState} from "../../hooks/notifications/useNotificationSate";
import NotificationItem from "../../components/notification/NotificationItem.jsx";

export default function Notifications() {
  const breadCrumbs = [
    {
      title: "Notification",
      url: "/account/notifications",
    },
  ];

  const breadCrumbsList = breadCrumbs?.map((item, index) => {
    return (
      <Link to={item.url} key={index}>
        <p className="crumb_link">/ {item.title}</p>
      </Link>
    );
  });

  return (
    <>
      <div
        className="d-flex w-100 main_house_con"
        style={{
          background: "none",
        }}>
        <AccountSideBar activeLink={'notification'} />

        <div className="col housing_con d-flex flex-column">
          <MainHeader
            showHeader={true}
            isAccount={true}
            breadCrums={breadCrumbs}
            activeLink={'notification'}
          />

          <div id="scrollable-section" className="col acct_cont_con pt-lg-0">
            <div className=" d-lg-none justify-content-between align-items-center mb-3">
              <div className="d-flex align-items-center gap-2">
                <Link to={"/"}>
                  <span className="crumb_icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="1em"
                      height="1em"
                      viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M13.45 2.533a2.25 2.25 0 0 0-2.9 0L3.8 8.228a2.25 2.25 0 0 0-.8 1.72v9.305c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V15.25c0-.68.542-1.232 1.217-1.25h2.566a1.25 1.25 0 0 1 1.217 1.25v4.003c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V9.947a2.25 2.25 0 0 0-.8-1.72z"
                      />
                    </svg>
                  </span>
                </Link>
                {breadCrumbsList}
              </div>
            </div>

            <RenderNotifications />

            <div className="col d-lg-none mt-4">
              <MainFooter />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

function RenderNotifications(){

  const {activeTab, ChangeTab} = useTabs(1);

  useNotificationState();

  const {notifications = [], notificationLoading} = useNotifications({});

  // Filter notifications based on active tab
  const filteredNotifications = React.useMemo(() => {
    if (!notifications || notifications.length === 0) return [];

    if (activeTab === 1) {
      // Show all notifications
      return notifications;
    } else if (activeTab === 2) {
      // Show only unread notifications
      return notifications.filter(item => !item.isRead);
    }
    return notifications;
  }, [notifications, activeTab]);

  if(notificationLoading) return <Spinner />

  return (
    <div className="acct_cont ">
      <div className="col">
        <div className="col d-flex gap-3 justify-content-between align-items-center mb-3">
          <div className="col d-flex gap-2">
            <p className={"dash_tab_link " + (activeTab == 1 && 'active')} onClick={() => ChangeTab(1)}>
              Notifications ({notifications.length})
            </p>
            {/* <p className={"dash_tab_link " + (activeTab == 2 && 'active')} onClick={() => ChangeTab(2)}>
              Unread ({notifications.filter(item => !item.isRead).length})
            </p> */}
          </div>
        </div>

        <div className="col">
          {filteredNotifications.length > 0 ? (
            filteredNotifications.map(item=> (
              <NotificationItem key={item._id} {...item} />
            ))
          ) : (
            <div className="text-center py-4">
              <p className="text-muted">
                {activeTab === 2 ? 'No unread notifications' : 'No notifications found'}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}