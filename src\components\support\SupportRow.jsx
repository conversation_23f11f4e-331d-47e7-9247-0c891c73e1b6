import {Link} from "react-router-dom";
import CustomDropdown from "../../pages/componets/utility/CustomDropdown.jsx";
import {convertToDays} from "../../vbrae-utils/lib/time.js";

export default function SupportRow({ticketNumber, subject, status, updatedAt, createdAt, _id, conversationId,singleitem}){


console.log('sac',singleitem);
    return (
        <tr className="acct_table_row">
            <td className="acct_table_data">
               <Link to={`/account/support-details/${_id}${conversationId?._id ? `?conversationId=${conversationId._id}` : ''}`}
                    className="acct_table_data">
                    {ticketNumber}
                </Link>
            </td>
            <td className="acct_table_data">
                {subject}
            </td>
            <td className="acct_table_data col-4">
             
               <span
  className={`
    acct_table_data_tag 
    ${
      singleitem?.status === "closed"
        ? "green"
        : !singleitem?.conversationId
        ? "blue"    // RESPONDING
        : "yellow"  // OPEN
    }
  `}
>
  {singleitem?.status === "closed"
    ? "CLOSE"
    : !singleitem?.conversationId
    ? "RESPONDING"
    : "OPEN"}
</span>

            </td>
            <td className="acct_table_data">{convertToDays(updatedAt)}</td>
            <td className="acct_table_data text-nowrap">
                {convertToDays(createdAt)}
            </td>
            <td className="acct_table_data position-relative">
                <CustomDropdown
                    trigger={() => (
                        <span className="acct_table_data_icon">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="20"
                                    height="20"
                                    viewBox="0 0 24 24">
                                  <path
                                      fill="currentColor"
                                      d="M12 3c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2m0 14c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2m0-7c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2"
                                  />
                                </svg>
                              </span>
                    )}
                    content={
                        <div className="acct_table_drop_cont">
                           <Link to={`/account/support-details/${_id}${conversationId?._id ? `?conversationId=${conversationId._id}` : ''}`}>

                                <p className="acct_table_drop_link">View</p>
                            </Link>
                            <Link>
                                <p className="acct_table_drop_link">Delete</p>
                            </Link>
                        </div>
                    }
                />
            </td>
        </tr>
    )
}