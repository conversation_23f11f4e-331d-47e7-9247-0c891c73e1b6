import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getTickets} from "../../api/ticket/getTickets.js";

export const useTickets = ({query}) => {
    const { data: tickets, loading: ticketsLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['tickets', query],
            queryFn: () => getTickets(query),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data,
    });

    return { tickets, ticketsLoading};
};