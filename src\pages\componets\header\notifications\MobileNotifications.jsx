import {Link} from "react-router-dom";
import {getAccessToken} from "../../../../vbrae-utils/index.js";
import {useNotifications} from "../../../../hooks/notifications/useNotifications.js";
import Spinner from "../../../../components/common/Spinner.jsx";
import CustomDropdown from "../../utility/CustomDropdown.jsx";
import {convertToDays} from "../../../../vbrae-utils/lib/time.js";

export default function MobileNotifications(){

    const hasUser = !!getAccessToken();
    const {notifications = [], notificationLoading} = useNotifications({limit: 5});

    if(notificationLoading) return <Spinner />

    const unreadCount = notifications.filter(item=> !item.isRead).length;

    return (
        <>
            {hasUser && <CustomDropdown
                trigger={() => (
                    <span className="mob_header_icon position-relative">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="1em"
                        height="1em"
                        viewBox="0 0 24 24">
                      <path
                          fill="currentColor"
                          d="M19.945 15.512c-.8-.786-1.619-1.6-1.619-5.44a7.835 7.835 0 0 0-6.539-7.717l-.046-.006a1.5 1.5 0 1 0-2.471.005l-.003-.005c-3.753.623-6.579 3.843-6.584 7.723v.001c0 3.84-.822 4.655-1.619 5.44A3.135 3.135 0 0 0 3.137 21h4.367a3 3 0 1 0 6 0h4.37a3.135 3.135 0 0 0 2.076-5.484l-.003-.003zm-9.441 6.613A1.127 1.127 0 0 1 9.379 21h2.251a1.127 1.127 0 0 1-1.126 1.125m7.36-3.376H3.138a.886.886 0 0 1-.625-1.509c1.34-1.34 2.418-2.612 2.418-7.17a5.572 5.572 0 0 1 11.144 0c0 4.578 1.089 5.84 2.418 7.17a.886.886 0 0 1-.625 1.509z"></path>
                    </svg>
                        {unreadCount > 0 && <span className="header_cart_num d-flex justify-content-center align-items-center position-absolute">
                      {unreadCount}
                    </span>}
                  </span>
                )}
                content={
                    <div className="header_drop_cont big position-absolute">
                        {notifications.length > 0 ? notifications.map((item, index) => (
                            <>
                                <Link to={"/account/notifications"} key={item._id || index}>
                                    <div className={`col-12 d-flex align-items-center gap-2 px-2 ${!item.isRead ? 'bg-light' : ''}`}>
                                        <div className="col-auto position-relative">
                                            <img
                                                src={item.entity?.templateCoverImage || "https://walrus-app-yy5iv.ondigitalocean.app/assets/images/logo.png"}
                                                alt=""
                                                className="acct_noti_img small"
                                            />
                                            {!item.isRead && (
                                                <span className="position-absolute top-0 start-100 translate-middle p-1 bg-primary border border-light rounded-circle">
                                                    <span className="visually-hidden">Unread</span>
                                                </span>
                                            )}
                                        </div>
                                        <div className="col">
                                            <p className={`acct_noti_title text-truncate col-8 ${!item.isRead ? 'fw-bold' : ''}`}>
                                                {item.title || item.message}
                                            </p>
                                            <p className="acct_noti_date small">{convertToDays(item.createdAt)}</p>
                                        </div>
                                    </div>
                                </Link>
                                <hr className="header_drop_line my-1"/>
                            </>
                        )) : <p className="header_drop_text text-center">No notifications</p>}
                        {notifications.length > 0 && <Link to={"/account/notifications"}>
                            <p className="header_drop_text text-center">See All</p>
                        </Link>}
                    </div>
                }
            />}
        </>
    )
}